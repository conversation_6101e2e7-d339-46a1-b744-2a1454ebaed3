#ifndef __STEPPER_MOTOR_API_H
#define __STEPPER_MOTOR_API_H

#include "sys.h"

// 步进电机参数配置
#define STEPS_PER_REVOLUTION    200     // 每圈步数 (1.8°步进电机)
#define STEP_ANGLE_DEG          1.8f    // 步进角度(度)
#define MAX_SPEED_RPM           300     // 最大转速(RPM)
#define MIN_SPEED_RPM           1       // 最小转速(RPM)
#define ACCELERATION_STEPS      50      // 加速步数

// 电机状态枚举
typedef enum {
    MOTOR_IDLE = 0,         // 空闲状态
    MOTOR_RUNNING,          // 运行中
    MOTOR_ACCELERATING,     // 加速中
    MOTOR_DECELERATING,     // 减速中
    MOTOR_POSITIONING       // 定位中
} MotorState_t;

// 电机方向枚举
typedef enum {
    MOTOR_CW = 0,          // 顺时针
    MOTOR_CCW = 1          // 逆时针
} MotorDirection_t;

// 电机控制结构体
typedef struct {
    int32_t current_position;      // 当前位置(步数)
    int32_t target_position;       // 目标位置(步数)
    uint16_t current_speed_rpm;    // 当前转速(RPM)
    uint16_t target_speed_rpm;     // 目标转速(RPM)
    MotorState_t state;            // 电机状态
    MotorDirection_t direction;    // 运行方向
    uint8_t motor_id;              // 电机ID (1或2)
} StepperMotor_t;

// 全局电机控制变量
extern StepperMotor_t motor1, motor2;

// ==================== 基础控制API ====================

/**
 * @brief 步进电机系统初始化
 * @param none
 * @retval none
 */
void StepperMotor_Init(void);

/**
 * @brief 设置电机方向
 * @param motor_id: 电机ID (1或2)
 * @param direction: 方向 (MOTOR_CW/MOTOR_CCW)
 * @retval none
 */
void StepperMotor_SetDirection(uint8_t motor_id, MotorDirection_t direction);

/**
 * @brief 启动连续运行
 * @param motor_id: 电机ID (1或2)
 * @param speed_rpm: 转速(RPM)
 * @param direction: 方向
 * @retval none
 */
void StepperMotor_StartContinuous(uint8_t motor_id, uint16_t speed_rpm, MotorDirection_t direction);

/**
 * @brief 停止电机
 * @param motor_id: 电机ID (1或2)
 * @retval none
 */
void StepperMotor_Stop(uint8_t motor_id);

/**
 * @brief 紧急停止所有电机
 * @param none
 * @retval none
 */
void StepperMotor_EmergencyStop(void);

// ==================== 精准定位API ====================

/**
 * @brief 相对位置移动(基于当前位置)
 * @param motor_id: 电机ID (1或2)
 * @param steps: 移动步数 (正数顺时针，负数逆时针)
 * @param speed_rpm: 移动速度(RPM)
 * @retval none
 */
void StepperMotor_MoveRelative(uint8_t motor_id, int32_t steps, uint16_t speed_rpm);

/**
 * @brief 绝对位置移动(移动到指定位置)
 * @param motor_id: 电机ID (1或2)
 * @param target_position: 目标位置(步数)
 * @param speed_rpm: 移动速度(RPM)
 * @retval none
 */
void StepperMotor_MoveAbsolute(uint8_t motor_id, int32_t target_position, uint16_t speed_rpm);

/**
 * @brief 角度移动(相对当前角度)
 * @param motor_id: 电机ID (1或2)
 * @param angle_deg: 移动角度(度) (正数顺时针，负数逆时针)
 * @param speed_rpm: 移动速度(RPM)
 * @retval none
 */
void StepperMotor_MoveAngle(uint8_t motor_id, float angle_deg, uint16_t speed_rpm);

/**
 * @brief 转动指定圈数
 * @param motor_id: 电机ID (1或2)
 * @param revolutions: 圈数 (正数顺时针，负数逆时针)
 * @param speed_rpm: 移动速度(RPM)
 * @retval none
 */
void StepperMotor_MoveRevolutions(uint8_t motor_id, float revolutions, uint16_t speed_rpm);

// ==================== 状态查询API ====================

/**
 * @brief 获取电机当前位置(步数)
 * @param motor_id: 电机ID (1或2)
 * @retval 当前位置(步数)
 */
int32_t StepperMotor_GetPosition(uint8_t motor_id);

/**
 * @brief 获取电机当前角度(度)
 * @param motor_id: 电机ID (1或2)
 * @retval 当前角度(度)
 */
float StepperMotor_GetAngle(uint8_t motor_id);

/**
 * @brief 检查电机是否在运行
 * @param motor_id: 电机ID (1或2)
 * @retval 1:运行中, 0:停止
 */
uint8_t StepperMotor_IsRunning(uint8_t motor_id);

/**
 * @brief 检查电机是否到达目标位置
 * @param motor_id: 电机ID (1或2)
 * @retval 1:已到达, 0:未到达
 */
uint8_t StepperMotor_IsPositionReached(uint8_t motor_id);

/**
 * @brief 获取电机状态
 * @param motor_id: 电机ID (1或2)
 * @retval 电机状态
 */
MotorState_t StepperMotor_GetState(uint8_t motor_id);

// ==================== 高级功能API ====================

/**
 * @brief 设置当前位置为零点
 * @param motor_id: 电机ID (1或2)
 * @retval none
 */
void StepperMotor_SetHome(uint8_t motor_id);

/**
 * @brief 回零点
 * @param motor_id: 电机ID (1或2)
 * @param speed_rpm: 回零速度(RPM)
 * @retval none
 */
void StepperMotor_GoHome(uint8_t motor_id, uint16_t speed_rpm);

/**
 * @brief 设置软件限位
 * @param motor_id: 电机ID (1或2)
 * @param min_position: 最小位置(步数)
 * @param max_position: 最大位置(步数)
 * @retval none
 */
void StepperMotor_SetSoftLimit(uint8_t motor_id, int32_t min_position, int32_t max_position);

/**
 * @brief 使能/禁用软件限位
 * @param motor_id: 电机ID (1或2)
 * @param enable: 1:使能, 0:禁用
 * @retval none
 */
void StepperMotor_EnableSoftLimit(uint8_t motor_id, uint8_t enable);

// ==================== 工具函数API ====================

/**
 * @brief 角度转换为步数
 * @param angle_deg: 角度(度)
 * @retval 步数
 */
int32_t StepperMotor_AngleToSteps(float angle_deg);

/**
 * @brief 步数转换为角度
 * @param steps: 步数
 * @retval 角度(度)
 */
float StepperMotor_StepsToAngle(int32_t steps);

/**
 * @brief RPM转换为PWM参数
 * @param speed_rpm: 转速(RPM)
 * @param arr: 输出ARR值
 * @param psc: 输出PSC值
 * @retval none
 */
void StepperMotor_RPMToPWM(uint16_t speed_rpm, uint16_t *arr, uint16_t *psc);

/**
 * @brief 系统定时器中断处理(需要在TIM2中断中调用)
 * @param none
 * @retval none
 */
void StepperMotor_TimerHandler(void);

#endif
